import tkinter as tk
from PIL import Image, ImageTk
import requests
from io import BytesIO
import random

class ImageBrowser:
    def __init__(self, master, image_urls):
        self.master = master
        self.image_urls = image_urls
        self.index = 0
        self.history = []
        
        self.label = tk.Label(master)
        self.label.pack()
        
        self.show_random_image()
        
        self.master.bind("<Left>", self.show_prev_image)
        self.master.bind("<Right>", self.show_random_image)

    def show_image(self, url):
        response = requests.get(url)
        img_data = response.content
        img = Image.open(BytesIO(img_data))
        
        # Get screen dimensions
        screen_width = self.master.winfo_screenwidth()
        screen_height = self.master.winfo_screenheight()
        
        # Calculate new image size keeping aspect ratio
        img_ratio = img.width / img.height
        screen_ratio = screen_width / screen_height
        
        if img_ratio > screen_ratio:
            new_width = screen_width
            new_height = int(screen_width / img_ratio)
        else:
            new_height = screen_height
            new_width = int(screen_height * img_ratio)
        
        img = img.resize((new_width, new_height), Image.ANTIALIAS)
        
        self.photo = ImageTk.PhotoImage(img)
        self.label.config(image=self.photo)
        self.label.image = self.photo  # Keep a reference to avoid garbage collection

    def show_random_image(self, event=None):
        self.history.append(self.index)  # Save current index to history
        self.index = random.randint(0, len(self.image_urls) - 1)
        self.show_image(self.image_urls[self.index])

    def show_prev_image(self, event=None):
        if self.history:
            self.index = self.history.pop()  # Get the last viewed index from history
            self.show_image(self.image_urls[self.index])

def load_image_urls(file_path):
    with open(file_path, 'r') as file:
        return [line.strip() for line in file.readlines()]

if __name__ == "__main__":
    file_path = '06seurl.txt'
    image_urls = load_image_urls(file_path)

    root = tk.Tk()
    root.geometry(f"{root.winfo_screenwidth()}x{root.winfo_screenheight()}")  # Make window fullscreen
    browser = ImageBrowser(root, image_urls)
    root.mainloop()