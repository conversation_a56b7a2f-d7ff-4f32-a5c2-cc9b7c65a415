import tkinter as tk
from PIL import Image, ImageTk
import requests
from io import BytesIO
import random

class ImageBrowser:
    def __init__(self, master, image_urls):
        self.master = master
        self.image_urls = image_urls
        self.index = 0
        self.history = []
        
        self.label = tk.Label(master)
        self.label.pack()
        
        self.show_random_image()
        
        self.master.bind("<Left>", self.show_prev_image)
        self.master.bind("<Right>", self.show_random_image)

    def show_image(self, url):
        try:
            print(f"Trying to load image from: {url}")
            response = requests.get(url)
            print(f"Response status code: {response.status_code}")
            print(f"Content-Type: {response.headers.get('content-type', 'Unknown')}")
            print(f"Content length: {len(response.content)} bytes")

            if response.status_code != 200:
                print(f"Failed to fetch image: HTTP {response.status_code}")
                return

            img_data = response.content
            if len(img_data) == 0:
                print("Empty response content")
                return

            img = Image.open(BytesIO(img_data))

            # Get screen dimensions
            screen_width = self.master.winfo_screenwidth()
            screen_height = self.master.winfo_screenheight()

            # Calculate new image size keeping aspect ratio
            img_ratio = img.width / img.height
            screen_ratio = screen_width / screen_height

            if img_ratio > screen_ratio:
                new_width = screen_width
                new_height = int(screen_width / img_ratio)
            else:
                new_height = screen_height
                new_width = int(screen_height * img_ratio)

            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

            self.photo = ImageTk.PhotoImage(img)
            self.label.config(image=self.photo)
            self.label.image = self.photo  # Keep a reference to avoid garbage collection
            print("Image loaded successfully!")

        except Exception as e:
            print(f"Error loading image: {e}")
            print(f"URL: {url}")
            # Try to show first few bytes of content for debugging
            if 'response' in locals():
                print(f"First 100 bytes: {response.content[:100]}")
            return

    def show_random_image(self, event=None):
        self.history.append(self.index)  # Save current index to history
        self.index = random.randint(0, len(self.image_urls) - 1)
        self.show_image(self.image_urls[self.index])

    def show_prev_image(self, event=None):
        if self.history:
            self.index = self.history.pop()  # Get the last viewed index from history
            self.show_image(self.image_urls[self.index])

def load_image_urls(file_path):
    with open(file_path, 'r') as file:
        return [line.strip() for line in file.readlines()]

if __name__ == "__main__":
    file_path = '06seurl.txt'
    image_urls = load_image_urls(file_path)

    root = tk.Tk()
    root.geometry(f"{root.winfo_screenwidth()}x{root.winfo_screenheight()}")  # Make window fullscreen
    browser = ImageBrowser(root, image_urls)
    root.mainloop()